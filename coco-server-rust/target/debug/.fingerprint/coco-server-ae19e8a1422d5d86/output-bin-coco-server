{"$message_type":"diagnostic","message":"unused imports: `HeaderMap`, `Router`, `body::Bytes`, `get`, and `post`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/connector_handler.rs","byte_start":66,"byte_end":77,"line_start":3,"line_end":3,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    body::Bytes,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":119,"byte_end":128,"line_start":5,"line_end":5,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":177,"byte_end":180,"line_start":7,"line_end":7,"column_start":15,"column_end":18,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":15,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":182,"byte_end":186,"line_start":7,"line_end":7,"column_start":20,"column_end":24,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":20,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":193,"byte_end":199,"line_start":8,"line_end":8,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Router,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handlers/connector_handler.rs","byte_start":66,"byte_end":83,"line_start":3,"line_end":4,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    body::Bytes,","highlight_start":5,"highlight_end":17},{"text":"    extract::{Query, State},","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":119,"byte_end":130,"line_start":5,"line_end":5,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":118,"byte_end":119,"line_start":5,"line_end":5,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":140,"byte_end":141,"line_start":5,"line_end":5,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"    http::{HeaderMap, StatusCode},","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/connector_handler.rs","byte_start":161,"byte_end":199,"line_start":6,"line_end":8,"column_start":19,"column_end":11,"is_primary":true,"text":[{"text":"    response::Json,","highlight_start":19,"highlight_end":20},{"text":"    routing::{get, post},","highlight_start":1,"highlight_end":26},{"text":"    Router,","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `HeaderMap`, `Router`, `body::Bytes`, `get`, and `post`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/connector_handler.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    body::Bytes,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::{Query, State},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::{HeaderMap, StatusCode},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    response::Json,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    routing::{get, post},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Router,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Router`, `get`, and `post`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/datasource_handler.rs","byte_start":177,"byte_end":180,"line_start":7,"line_end":7,"column_start":15,"column_end":18,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":15,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/datasource_handler.rs","byte_start":182,"byte_end":186,"line_start":7,"line_end":7,"column_start":20,"column_end":24,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":20,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/datasource_handler.rs","byte_start":193,"byte_end":199,"line_start":8,"line_end":8,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Router,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handlers/datasource_handler.rs","byte_start":161,"byte_end":199,"line_start":6,"line_end":8,"column_start":19,"column_end":11,"is_primary":true,"text":[{"text":"    response::Json,","highlight_start":19,"highlight_end":20},{"text":"    routing::{get, post},","highlight_start":1,"highlight_end":26},{"text":"    Router,","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Router`, `get`, and `post`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/datasource_handler.rs:7:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    routing::{get, post},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Router,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Deserialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/sso_handler.rs","byte_start":117,"byte_end":128,"line_start":6,"line_end":6,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/handlers/sso_handler.rs","byte_start":117,"byte_end":130,"line_start":6,"line_end":6,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/sso_handler.rs","byte_start":116,"byte_end":117,"line_start":6,"line_end":6,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/sso_handler.rs","byte_start":139,"byte_end":140,"line_start":6,"line_end":6,"column_start":35,"column_end":36,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":35,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Deserialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/sso_handler.rs:6:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HeaderValue` and `extract::Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/static_handler.rs","byte_start":32,"byte_end":45,"line_start":3,"line_end":3,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    extract::Path,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/static_handler.rs","byte_start":77,"byte_end":88,"line_start":4,"line_end":4,"column_start":31,"column_end":42,"is_primary":true,"text":[{"text":"    http::{header, HeaderMap, HeaderValue, Request},","highlight_start":31,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handlers/static_handler.rs","byte_start":26,"byte_end":45,"line_start":2,"line_end":3,"column_start":15,"column_end":18,"is_primary":true,"text":[{"text":"    body::Body,","highlight_start":15,"highlight_end":16},{"text":"    extract::Path,","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/static_handler.rs","byte_start":75,"byte_end":88,"line_start":4,"line_end":4,"column_start":29,"column_end":42,"is_primary":true,"text":[{"text":"    http::{header, HeaderMap, HeaderValue, Request},","highlight_start":29,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `HeaderValue` and `extract::Path`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/static_handler.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::Path,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::{header, HeaderMap, HeaderValue, Request},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this import is redundant","code":{"code":"clippy::single_component_path_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/main.rs","byte_start":453,"byte_end":476,"line_start":21,"line_end":21,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"use tracing_subscriber;","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_component_path_imports","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::single_component_path_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove it entirely","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":453,"byte_end":477,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing_subscriber;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this import is redundant\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:21:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing_subscriber;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: remove it entirely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_component_path_imports\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::single_component_path_imports)]` on by default\u001b[0m\n\n"}
